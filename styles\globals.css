/* globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Neue Machina';
  src: url('/fonts/Neue Machina.woff2') format('woff2'),
    url('/fonts/Neue Machina.woff') format('woff');
  font-weight: 400 900;
  font-style: normal;
  font-display: block;
}

/* Additional font-face for extra bold weight to ensure consistency */
@font-face {
  font-family: 'Neue Machina Bold';
  src: url('/fonts/Neue Machina.woff2') format('woff2'),
    url('/fonts/Neue Machina.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: block;
}

/* Hero section font classes with proper fallbacks */
.hero-heading {
  font-family: 'Neue Machina Bold', 'Neue Machina', 'Inter', system-ui, -apple-system,
    BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
  font-weight: 900 !important;
  font-variation-settings: 'wght' 900;
  letter-spacing: -0.02em;
  line-height: 0.9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Ensure font weight never changes */
  font-synthesis: none;
  -webkit-font-synthesis: none;
  -moz-font-synthesis: none;
}

.hero-text {
  font-family: 'Neue Machina', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI',
    Roboto, sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
}

/* Force font weight consistency across all states */
.hero-heading,
.hero-heading * {
  font-weight: 900 !important;
}

/* Prevent font weight changes during loading or any state changes */
.hero-heading {
  font-display: block !important;
}

/* Additional specificity to ensure font weight never changes */
h1.hero-heading {
  font-weight: 900 !important;
}

h1.hero-heading.font-black {
  font-weight: 900 !important;
}

/* Ultimate font weight protection - covers all possible selectors */
section h1.hero-heading,
div h1.hero-heading,
.hero-heading[class*='font'],
.hero-heading[class*='text'] {
  font-weight: 900 !important;
  font-variation-settings: 'wght' 900 !important;
}

/* Prevent any CSS framework from overriding font weight */
.hero-heading {
  font-weight: 900 !important;
  font-variation-settings: 'wght' 900 !important;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Custom scrollbar - removed backgrounds */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  /* background removed - now transparent */
}
::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3); /* Made semi-transparent */
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.5); /* Made semi-transparent */
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
.animate-slideUp {
  animation: slideUp 0.5s ease-out;
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}
.animate-blob {
  animation: blob 7s infinite;
}
.animation-delay-2000 {
  animation-delay: 2s;
}
.animation-delay-4000 {
  animation-delay: 4s;
}

/* Typing cursor animation */
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: currentColor;
  margin-left: 2px;
  animation: blink 1s step-end infinite;
}

/* Marquee Animation */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}
.animate-marquee {
  animation-name: marquee;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}
.animate-marquee:hover {
  animation-play-state: paused;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
