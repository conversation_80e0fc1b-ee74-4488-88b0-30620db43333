'use client';

import clsx from 'clsx';
import { LayoutDashboard, LogOut, Menu, Settings, User, X } from 'lucide-react';
import { signOut, useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './Header.module.css';

const NAV_ITEMS = [
  { name: 'Challenges', href: '/contest' },
  { name: 'Leaderboard', href: '/leaderboard' },
  { name: 'Donate', href: '/donate', special: true },
  { name: 'About', href: '/about' },
  { name: 'Contact', href: '/contact' },
];

const defaultAvatar = '/images/user.svg';

export default function Header() {
  const { data: session, status } = useSession();
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setUserMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Toggle mobile menu and lock body scroll
  const toggleMobileMenu = useCallback(() => {
    setMobileMenuOpen(prev => {
      const isOpening = !prev;
      document.body.style.overflow = isOpening ? 'hidden' : 'auto';
      return isOpening;
    });
  }, []);

  const closeMobileMenu = useCallback(() => {
    setMobileMenuOpen(false);
    document.body.style.overflow = 'auto';
  }, []);

  const toggleUserMenu = useCallback(() => setUserMenuOpen(prev => !prev), []);

  const handleSignOut = useCallback(async () => {
    await signOut({ callbackUrl: '/' });
  }, []);

  // Handle outside clicks for the user dropdown
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(e.target as Node)) {
        setUserMenuOpen(false);
      }
    };
    if (isUserMenuOpen) {
      document.addEventListener('mousedown', handleOutsideClick);
    }
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [isUserMenuOpen]);

  // Handle scroll to change header appearance
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const renderAvatar = (size = 40) => {
    const imageUrl = session?.user?.image || defaultAvatar;
    return (
      <Image
        src={imageUrl}
        alt={session?.user?.name || 'User Avatar'}
        width={size}
        height={size}
        className="rounded-full object-cover"
        unoptimized={imageUrl.startsWith('data:')}
      />
    );
  };

  return (
    <>
      <header
        className={clsx(
          'relative top-12 left-0 right-0 z-50 transition-all duration-300 ease-in-out bg-transparent',
          isScrolled ? 'py-0' : 'py-0',
        )}
      >
        <nav
          className={clsx(
            'container mx-auto max-w-7xl flex items-center justify-center px-2 py-0 rounded-2xl transition-all duration-300 relative',
            isScrolled ? 'bg-black/50 backdrop-blur-lg ring-1 ring-white/10' : 'bg-transparent',
          )}
        >
          {/* Logo - positioned absolutely on the left */}
          <div className="absolute left-2 flex items-center">
            <Link href="/" onClick={closeMobileMenu} className="group flex-shrink-0">
              <img
                src="/logos/typing-logo-home-orange.svg"
                alt="Typing.com.co Logo"
                className="h-7 w-auto transition-transform duration-300 group-hover:scale-110"
              />
            </Link>
          </div>

          {/* === CENTERED DESKTOP NAVIGATION === */}
          <div className="hidden lg:flex items-center justify-center">
            {/* The main navbar container with the custom gradient */}
            <div className="relative flex items-center rounded-full bg-gradient-to-r from-[#FF3C00] via-white to-[#FF3C00] shadow-lg p-0.5">
              {/* Left side navigation items */}
              <div className="flex items-center gap-6">
                {NAV_ITEMS.slice(0, 2).map(item => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block text-sm font-extrabold transition-all duration-200 text-black hover:bg-black/10 rounded-full px-6 py-0"
                    style={{
                      fontFamily: "'Neue Machina Bold', 'Neue Machina', sans-serif",
                      fontWeight: 900,
                    }}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>

              {/* Center Donate button - touches all borders */}
              <div className="mx-1">
                <Link
                  href="/donate"
                  className="block text-sm font-bold transition-all duration-200 bg-black text-white hover:bg-neutral-800 shadow-md rounded-full px-6 py-2 my-[-2px] relative z-10"
                >
                  Donate
                </Link>
              </div>

              {/* Right side navigation items */}
              <div className="flex items-center gap-6">
                {NAV_ITEMS.slice(3).map(item => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block text-sm font-extrabold transition-all duration-200 text-black hover:bg-black/10 rounded-full px-6 py-0"
                    style={{
                      fontFamily: "'Neue Machina Bold', 'Neue Machina', sans-serif",
                      fontWeight: 900,
                    }}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Auth Controls & Mobile Menu Trigger - positioned absolutely on the right */}
          <div className="absolute right-2 flex items-center gap-4">
            {/* Desktop Auth */}
            <div className="hidden lg:flex items-center gap-2" ref={userMenuRef}>
              {status === 'loading' && (
                <div className="w-10 h-10 bg-neutral-700/50 rounded-full animate-pulse"></div>
              )}
              {status === 'unauthenticated' && (
                <>
                  <Link
                    href="/sign-in"
                    className="px-4 py-2 text-sm font-medium text-neutral-300 hover:bg-neutral-700/80 hover:text-white rounded-full transition-colors"
                  >
                    Login
                  </Link>
                  {/* Updated Sign-Up button with the new brand color */}
                  <Link
                    href="/sign-up"
                    className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-[#FF3C00] to-red-600 hover:brightness-110 rounded-full transition-all"
                  >
                    Sign Up
                  </Link>
                </>
              )}
              {status === 'authenticated' && (
                <div className="relative">
                  <button onClick={toggleUserMenu} className="flex items-center gap-2 group">
                    <div className="w-10 h-10 rounded-full ring-2 ring-offset-2 ring-offset-neutral-900 ring-transparent group-hover:ring-orange-500 transition-all duration-300">
                      {renderAvatar(40)}
                    </div>
                  </button>
                  {isUserMenuOpen && (
                    <div
                      className={clsx(
                        'absolute right-0 top-full mt-4 w-60 origin-top-right rounded-xl bg-neutral-900/90 backdrop-blur-md ring-1 ring-white/10',
                        styles['dropdown-enter'],
                      )}
                    >
                      <div className="p-4 border-b border-white/10">
                        <p className="font-semibold text-white truncate">{session.user.name}</p>
                        <p className="text-sm text-neutral-400 truncate">{session.user.email}</p>
                      </div>
                      <div className="p-2">
                        <Link
                          href="/dashboard"
                          onClick={toggleUserMenu}
                          className="flex items-center gap-3 w-full px-3 py-2 text-sm text-neutral-200 hover:bg-white/10 rounded-md transition-colors"
                        >
                          <LayoutDashboard size={16} /> Dashboard
                        </Link>
                        <Link
                          href="/profile"
                          onClick={toggleUserMenu}
                          className="flex items-center gap-3 w-full px-3 py-2 text-sm text-neutral-200 hover:bg-white/10 rounded-md transition-colors"
                        >
                          <User size={16} /> Profile
                        </Link>
                        <Link
                          href="/settings"
                          onClick={toggleUserMenu}
                          className="flex items-center gap-3 w-full px-3 py-2 text-sm text-neutral-200 hover:bg-white/10 rounded-md transition-colors"
                        >
                          <Settings size={16} /> Settings
                        </Link>
                      </div>
                      <div className="p-2 border-t border-white/10">
                        <button
                          onClick={handleSignOut}
                          className="flex items-center gap-3 w-full px-3 py-2 text-sm text-red-400 hover:bg-red-500/10 rounded-md transition-colors"
                        >
                          <LogOut size={16} /> Logout
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Mobile Menu Trigger */}
            <button
              onClick={toggleMobileMenu}
              className="lg:hidden p-2 text-white"
              aria-label="Open menu"
            >
              <Menu size={24} />
            </button>
          </div>
        </nav>
      </header>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className={clsx(
            'fixed inset-0 z-50 bg-black/80 backdrop-blur-lg',
            styles['mobile-menu-enter'],
          )}
        >
          <div className="container mx-auto h-full flex flex-col p-4">
            <div className="flex justify-between items-center">
              <Link href="/" onClick={closeMobileMenu}>
                <img src="/logos/typing-logo-home-orange.svg" alt="Logo" className="h-7" />
              </Link>
              <button onClick={closeMobileMenu} className="p-2 text-white" aria-label="Close menu">
                <X size={28} />
              </button>
            </div>

            <div className="flex-1 flex flex-col justify-center items-center gap-10">
              <nav>
                <ul className="flex flex-col items-center gap-14">
                  {NAV_ITEMS.map((item, i) => (
                    <li
                      key={item.name}
                      className={styles['nav-item-animate']}
                      style={{ animationDelay: `${i * 100}ms` }}
                    >
                      <Link
                        href={item.href}
                        onClick={closeMobileMenu}
                        className={clsx(
                          'text-3xl font-extralight transition-colors py-2 px-6',
                          item.special
                            ? 'text-transparent bg-clip-text bg-gradient-to-r from-[#FF3C00] to-red-500'
                            : 'text-neutral-300 hover:text-white',
                        )}
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>

              <div className="w-full max-w-xs pt-8 border-t border-white/10">
                {status === 'authenticated' ? (
                  <div className="flex flex-col items-center gap-4">
                    <div className="w-20 h-20 rounded-full">{renderAvatar(80)}</div>
                    <p className="text-xl font-semibold text-white">{session.user.name}</p>
                    <button
                      onClick={handleSignOut}
                      className="w-full text-center py-3 text-red-400 bg-red-500/10 rounded-lg"
                    >
                      Logout
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-col gap-4">
                    <Link
                      href="/sign-in"
                      onClick={closeMobileMenu}
                      className="w-full text-center py-3 bg-neutral-700/80 text-white rounded-lg"
                    >
                      Login
                    </Link>
                    {/* Updated Sign-Up button for mobile */}
                    <Link
                      href="/sign-up"
                      onClick={closeMobileMenu}
                      className="w-full text-center py-3 bg-gradient-to-r from-[#FF3C00] to-red-600 text-white rounded-lg"
                    >
                      Sign Up
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
